import 'package:flutter/material.dart';
import '../services/pdf_service.dart';

class MiniFactureTestPage extends StatefulWidget {
  const MiniFactureTestPage({super.key});

  @override
  State<MiniFactureTestPage> createState() => _MiniFactureTestPageState();
}

class _MiniFactureTestPageState extends State<MiniFactureTestPage> {
  final _formKey = GlobalKey<FormState>();
  final _nomClientController = TextEditingController(text: '<PERSON>');
  final _numeroClientController = TextEditingController(text: 'CLI-001');
  final _lieuLivraisonController = TextEditingController(text: 'Dakar, Plateau');
  final _resteAPayerController = TextEditingController(text: '15000');
  
  bool _isGenerating = false;

  @override
  void dispose() {
    _nomClientController.dispose();
    _numeroClientController.dispose();
    _lieuLivraisonController.dispose();
    _resteAPayerController.dispose();
    super.dispose();
  }

  Future<void> _generateMiniInvoice() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      await PDFService.generateAndDownloadMiniInvoice(
        nomClient: _nomClientController.text,
        numeroClient: _numeroClientController.text,
        lieuLivraison: _lieuLivraisonController.text,
        resteAPayer: double.parse(_resteAPayerController.text),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Mini facture générée avec succès !'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la génération : $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Mini Facture 5x3"'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Informations Client',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _nomClientController,
                        decoration: const InputDecoration(
                          labelText: 'Nom du client',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez saisir le nom du client';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _numeroClientController,
                        decoration: const InputDecoration(
                          labelText: 'Numéro client',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.numbers),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez saisir le numéro client';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _lieuLivraisonController,
                        decoration: const InputDecoration(
                          labelText: 'Lieu de livraison',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.location_on),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez saisir le lieu de livraison';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _resteAPayerController,
                        decoration: const InputDecoration(
                          labelText: 'Reste à payer (FCFA)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.money),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez saisir le montant';
                          }
                          if (double.tryParse(value) == null) {
                            return 'Veuillez saisir un montant valide';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue[800],
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Format Mini Facture',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[800],
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '• Dimensions : 5 x 3 pouces (12,7 x 7,6 cm)\n'
                        '• Optimisé pour imprimantes thermiques\n'
                        '• Design noir et blanc uniquement\n'
                        '• QR codes pour site, WhatsApp et paiements\n'
                        '• Logo HCP DESIGN inclus',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _isGenerating ? null : _generateMiniInvoice,
                icon: _isGenerating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.picture_as_pdf),
                label: Text(
                  _isGenerating ? 'Génération en cours...' : 'Générer Mini Facture PDF',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[800],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}